/**
 * Custom Promise Implementation
 *
 * This is a complete Promise implementation from scratch that demonstrates
 * understanding of asynchronous JavaScript, state management, and the Promise/A+ specification.
 *
 * Key features implemented:
 * - Promise states: pending, fulfilled, rejected
 * - Executor function with resolve/reject callbacks
 * - .then() method with proper chaining
 * - .catch() method for error handling
 * - Asynchronous execution handling
 * - Proper error propagation
 */

class MyPromise {
    // Promise states as constants
    static PENDING = 'pending';
    static FULFILLED = 'fulfilled';
    static REJECTED = 'rejected';

    constructor(executor) {
        // Initialize promise state
        this.state = MyPromise.PENDING;
        this.value = undefined; // Will hold the resolved value or rejection reason
        this.onFulfilledCallbacks = []; // Array to store success callbacks
        this.onRejectedCallbacks = [];  // Array to store error callbacks

        // Define resolve function
        const resolve = (value) => {
            // Only allow state change from pending
            if (this.state === MyPromise.PENDING) {
                this.state = MyPromise.FULFILLED;
                this.value = value;

                // Execute all queued success callbacks
                this.onFulfilledCallbacks.forEach(callback => {
                    callback(value);
                });
            }
        };

        // Define reject function
        const reject = (reason) => {
            // Only allow state change from pending
            if (this.state === MyPromise.PENDING) {
                this.state = MyPromise.REJECTED;
                this.value = reason;

                // Execute all queued error callbacks
                this.onRejectedCallbacks.forEach(callback => {
                    callback(reason);
                });
            }
        };

        // Execute the executor function immediately
        try {
            executor(resolve, reject);
        } catch (error) {
            // If executor throws, reject the promise
            reject(error);
        }
    }

    /**
     * The .then() method - core of Promise chaining
     * Returns a new Promise to enable chaining
     */
    then(onFulfilled, onRejected) {
        // Return a new Promise for chaining
        return new MyPromise((resolve, reject) => {

            // Helper function to handle fulfilled state
            const handleFulfilled = (value) => {
                try {
                    if (typeof onFulfilled === 'function') {
                        const result = onFulfilled(value);

                        // If result is a Promise, wait for it to resolve
                        if (result instanceof MyPromise) {
                            result.then(resolve, reject);
                        } else {
                            resolve(result);
                        }
                    } else {
                        // If no onFulfilled handler, pass value through
                        resolve(value);
                    }
                } catch (error) {
                    reject(error);
                }
            };

            // Helper function to handle rejected state
            const handleRejected = (reason) => {
                try {
                    if (typeof onRejected === 'function') {
                        const result = onRejected(reason);

                        // If result is a Promise, wait for it to resolve
                        if (result instanceof MyPromise) {
                            result.then(resolve, reject);
                        } else {
                            resolve(result); // Note: resolve, not reject (error was handled)
                        }
                    } else {
                        // If no onRejected handler, propagate the rejection
                        reject(reason);
                    }
                } catch (error) {
                    reject(error);
                }
            };

            // Handle based on current state
            if (this.state === MyPromise.FULFILLED) {
                // Promise already resolved - execute callback asynchronously
                setTimeout(() => handleFulfilled(this.value), 0);
            } else if (this.state === MyPromise.REJECTED) {
                // Promise already rejected - execute callback asynchronously
                setTimeout(() => handleRejected(this.value), 0);
            } else {
                // Promise still pending - queue callbacks
                this.onFulfilledCallbacks.push((value) => {
                    setTimeout(() => handleFulfilled(value), 0);
                });
                this.onRejectedCallbacks.push((reason) => {
                    setTimeout(() => handleRejected(reason), 0);
                });
            }
        });
    }

    /**
     * The .catch() method - syntactic sugar for .then(null, onRejected)
     */
    catch(onRejected) {
        return this.then(null, onRejected);
    }

    /**
     * Static method: Promise.resolve()
     * Creates a resolved promise with the given value
     */
    static resolve(value) {
        return new MyPromise((resolve) => {
            resolve(value);
        });
    }

    /**
     * Static method: Promise.reject()
     * Creates a rejected promise with the given reason
     */
    static reject(reason) {
        return new MyPromise((resolve, reject) => {
            reject(reason);
        });
    }
}

// ============================================================================
// DEMONSTRATION AND TESTING
// ============================================================================

console.log('=== Custom Promise Implementation Demo ===\n');

// Test 1: Basic Promise creation and resolution
console.log('Test 1: Basic Promise Resolution');
const promise1 = new MyPromise((resolve, reject) => {
    console.log('  Executor running...');
    setTimeout(() => {
        resolve('Success! 🎉');
    }, 1000);
});

promise1.then((value) => {
    console.log('  Resolved with:', value);
}).catch((error) => {
    console.log('  Caught error:', error);
});

// Test 2: Promise chaining
console.log('\nTest 2: Promise Chaining');
MyPromise.resolve(10)
    .then(value => {
        console.log('  Step 1:', value);
        return value * 2;
    })
    .then(value => {
        console.log('  Step 2:', value);
        return value + 5;
    })
    .then(value => {
        console.log('  Final result:', value);
    });

// Test 3: Error handling and propagation
console.log('\nTest 3: Error Handling');
new MyPromise((resolve, reject) => {
    setTimeout(() => {
        reject(new Error('Something went wrong! ❌'));
    }, 500);
})
.then(value => {
    console.log('  This should not run');
})
.catch(error => {
    console.log('  Caught error:', error.message);
    return 'Recovered from error';
})
.then(value => {
    console.log('  Recovery successful:', value);
});

// Test 4: Synchronous resolution
console.log('\nTest 4: Synchronous Resolution');
const syncPromise = new MyPromise((resolve) => {
    resolve('Immediate resolution');
});

syncPromise.then(value => {
    console.log('  Sync result:', value);
});

console.log('\n=== All tests initiated. Watch for async results... ===');